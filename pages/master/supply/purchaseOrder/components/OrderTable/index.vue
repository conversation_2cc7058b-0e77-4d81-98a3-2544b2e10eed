<template>
  <div class="order-table">
    <ma-crud
      ref="crudRef"
      v-model="crudBinding"
      :options="crudOptions"
      :columns="columns"
      @request="handleRequest"
    >
      <!-- 订单信息插槽 -->
      <template #orderInfo="{ record }">
        <div class="order-info-card">
          <div class="info-row">
            <span class="label">系统订单编号：</span>
            <span class="order-number">{{ record.originalOrderNumber }}</span>
          </div>
          <div class="info-row">
            <span class="label">三方订单编号：</span>
            <span class="order-number">{{ record.thirdPartyOrderSn }}</span>
          </div>
          <div class="info-row">
            <span class="label">采购订单编号：</span>
            <span class="order-number">{{ record.orderNumber }}</span>
          </div>
          <div class="info-row">
            <span class="label">下单账号：</span>
            <span class="order-account">{{ record.buyerAccount }}</span>
          </div>
          <div class="info-row">
            <span class="label">采购时间：</span>
            <span>{{ formatDateTime(record.purchaseTime) }}</span>
          </div>
          <div class="info-row">
            <span class="label">下单时间：</span>
            <span>{{ formatDateTime(record.orderTime) }}</span>
          </div>
          <div class="info-row">
            <span class="label">订单渠道：</span>
            <span>{{ record.channelName || "未知渠道" }}</span>
          </div>
          <div class="info-row">
            <span class="label">店铺：</span>
            <span>{{ record.storeName }}</span>
          </div>
          <div class="info-row">
            <span class="label">跟单员：</span>
            <span>{{ record.follower }}</span>
          </div>
          <div class="info-row">
            <span class="label">采购员：</span>
            <span>{{ record.purchaser }}</span>
          </div>
          <div class="info-row">
            <span class="label">订单类型：</span>
            <span>{{
              OrderTypeEnum.getTypeText(record.originOrderInfo?.orderType)
            }}</span>
          </div>

          <!-- 产品线过期时间信息 -->
          <div class="info-row">
            <span class="label">产品线过期时间：</span>
            <span
              class="expire-time-display"
              :class="getProductLineExpireDisplayClass(record)"
            >
              {{ getFirstProductLineExpireTime(record) }}
            </span>
          </div>
        </div>
      </template>

      <!-- 商品信息插槽 -->
      <template #productInfo="{ record }">
        <div class="product-info-card">
          <!-- 多商品展示 -->
          <div
            v-if="record.products && record.products.length > 0"
            class="products-container"
          >
            <div class="products-header">
              <span class="products-count"
                >共{{ record.products.length }}种商品</span
              >
              <span class="total-quantity">{{ record.totalQuantity }}件</span>
            </div>

            <div class="products-list">
              <div
                v-for="(product, index) in record.products"
                :key="product.id"
                class="product-item"
                :class="{ 'has-border': index < record.products.length - 1 }"
              >
                <div
                  class="product-image"
                  @click="handleProductImageClick(product.productCode)"
                >
                  <a-image
                    :src="product.productImage"
                    width="50"
                    height="50"
                    fit="cover"
                    :preview="false"
                    class="clickable-image"
                  />
                </div>
                <div class="product-details">
                  <div>
                    <div class="product-name">{{ product.productName }}</div>
                    <div class="product-sku">SKU: {{ product.sku }}</div>
                    <div class="product-code">
                      编码: {{ product.productCode }}
                    </div>
                    <div class="product-spec">规格：{{ product.specification || "-" }}</div>
                    <div class="product-tax-category">
                      <span class="tax-label">税收分类：</span>
                      <a-button
                        type="text"
                        size="small"
                        class="tax-link"
                        @click="handleSelectTaxClassification(product, record)"
                      >
                        {{ product.taxClassification?.label || "点击选择" }}
                        <template #icon><icon-edit /></template>
                      </a-button>
                    </div>
                    <div class="product-price-qty">
                      <span class="price">单价:{{ product.unitPrice }}</span>
                      <span class="quantity">数量:{{ product.quantity }}</span>
                    </div>
                    <div class="product-supplier-info">
                      <div class="supplier-row">
                        <span class="supplier-label">实际供应商：</span>
                        <a-button
                          type="text"
                          size="mini"
                          class="supplier-link"
                          @click="handleSelectProductSupplier(product, record)"
                        >
                          {{ product.actualSupplier || "点击选择" }}
                          <template #icon><icon-edit /></template>
                        </a-button>
                      </div>
                      <div class="supplier-row">
                        <span class="supplier-label">建议供应商：</span>
                        <span class="supplier-text">{{
                          product.suggestedSupplier
                        }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="product-cost-info">
                    <span
                      class="cost-info-text"
                      style="display: flex; flex-direction: column"
                    >
                      <span>实际成本：{{ product.actualCost }}</span>
                      <span>巡检成本：{{ product.inspectionCost }}</span>
                      <span>毛利率：{{ product.profitRate }}</span>
                    </span>
                  </div>
                  <div style="display: flex; flex-direction: column">
                    <div class="cost-item">
                      <span class="cost-label">成本总价：</span>
                      <div class="cost-input-wrapper">
                        <span class="currency-symbol">¥</span>
                        <a-input
                          v-model="product.costTotal"
                          size="small"
                          class="cost-input"
                          placeholder="请输入成本总价"
                          @blur="
                            (e) =>
                              handleProductCostTotalChange(
                                product,
                                record,
                                e.target.value
                              )
                          "
                          @press-enter="
                            (e) =>
                              handleProductCostTotalChange(
                                product,
                                record,
                                e.target.value
                              )
                          "
                          @input="
                            (value) =>
                              handleProductCostTotalInput(product, value)
                          "
                        />
                      </div>
                    </div>

                    <!-- 亏损信息 - 当成本总价大于巡检成本时显示 -->
                    <div v-if="isProductLoss(product)" class="loss-info">
                      <div class="cost-item loss-amount">
                        <span class="cost-label">当前亏损：</span>
                        <span class="cost-content loss-text"
                          >¥{{ calculateProductLossAmount(product) }}</span
                        >
                      </div>
                      <div class="loss-reason-section">
                        <a-select
                          :model-value="product.lossReason"
                          placeholder="请选择亏损原因"
                          size="small"
                          class="loss-reason-select"
                          @change="
                            (value) =>
                              handleProductLossReasonChange(
                                product,
                                record,
                                value
                              )
                          "
                        >
                          <a-option value="freight_loss">运费亏损</a-option>
                          <a-option value="packaging_loss"
                            >特殊包装亏损</a-option
                          >
                          <a-option value="installation_loss"
                            >安装费亏损</a-option
                          >
                          <a-option value="tax_loss">税金亏损</a-option>
                          <a-option value="no_quote">未参与报价</a-option>
                          <a-option value="low_quantity">采购数量少</a-option>
                          <a-option value="quote_error">开发报价错误</a-option>
                          <a-option value="quick_removal"
                            >快上快下未及时下架</a-option
                          >
                          <a-option value="discontinued">产品型号停产</a-option>
                          <a-option value="relationship_order"
                            >客情自然单</a-option
                          >
                          <a-option value="other">其他</a-option>
                        </a-select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 兼容旧的单商品数据结构 -->
          <div v-else-if="record.productName" class="single-product">
            <div class="product-image">
              <a-image
                :src="record.productImage"
                width="60"
                height="60"
                fit="cover"
                :preview="false"
              />
            </div>
            <div class="product-details">
              <div class="product-name">{{ record.productName }}</div>
              <div class="product-sku">SKU: {{ record.sku }}</div>
              <div class="product-code">商品编码: {{ record.productCode }}</div>
              <div class="product-spec">
                规格型号: {{ record.specification }}
              </div>
              <div class="product-tax-category">
                <span class="tax-label">税收分类：</span>
                <a-button
                  type="text"
                  size="small"
                  class="tax-link"
                  @click="handleSelectTaxClassification(record, record)"
                >
                  {{ record.taxClassification?.label || "点击选择" }}
                  <template #icon><icon-edit /></template>
                </a-button>
              </div>
              <div class="product-cost-info">
                <span class="cost-info-text">
                  实：{{ record.actualCost }}/巡：{{
                    record.inspectionCost
                  }}
                  毛利率：{{ record.profitRate }}
                </span>
              </div>
              <div class="product-cost-total">
                <span class="cost-total-label">成本总价：</span>
                <span class="cost-total-value">¥{{ record.costTotal }}</span>
              </div>
              <div class="product-supplier-info">
                <div class="supplier-row">
                  <span class="supplier-label">实际：</span>
                  <a-button
                    type="text"
                    size="mini"
                    class="supplier-link"
                    @click="handleSelectProductSupplier(record, record)"
                  >
                    {{ record.actualSupplier || "点击选择" }}
                    <template #icon><icon-edit /></template>
                  </a-button>
                </div>
                <div class="supplier-row">
                  <span class="supplier-label">建议：</span>
                  <span class="supplier-text">{{
                    record.suggestedSupplier
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 采购成本信息插槽 -->
      <template #costInfo="{ record }">
        <div class="cost-info-card">
          <div class="cost-item">
            <span class="cost-label">商品总额：</span>
            <span class="cost-content"
              >¥{{ calculateProductsTotal(record) }}</span
            >
          </div>
          <div class="cost-item">
            <span class="cost-label">运费：</span>
            <span class="cost-content">¥{{ record.freight }}</span>
          </div>
          <div class="cost-item">
            <span class="cost-label">实付金额：</span>
            <span class="cost-content total-amount"
              >¥{{ record.totalAmount }}</span
            >
          </div>
          <div class="cost-item">
            <span class="cost-sub-label"
              >共{{ record.totalQuantity }}件商品</span
            >
          </div>
          <div class="cost-item">
            <span class="cost-label">总毛利率：</span>
            <span class="cost-content profit" style="color: red">{{
              record.grossProfitRate
            }}</span>
          </div>
        </div>
      </template>

      <!-- 采购信息插槽 -->
      <template #purchaseInfo="{ record }">
        <div class="purchase-info-card">
          <div class="purchase-item">
            <span class="purchase-label">买家/收货人：</span>
            <span class="purchase-content">{{ record.actualReceiver }}</span>
            <span class="purchase-content ml-5">{{ record.actualPhone }}</span>
          </div>
          <div class="purchase-item">
            <span class="purchase-label">实际收货地址：</span>
            <span>{{ record.actualAddress }}</span>
          </div>
          <div class="purchase-item">
            <span class="purchase-label">物流情况：</span>
            <span class="purchase-content">{{
              record.logisticsInfo || "-"
            }}</span>
          </div>
          <div class="purchase-item">
            <span class="purchase-label">采购进度：</span>
            <div class="purchase-progress-wrapper">
              <a-select
                :model-value="record.purchaseProgress"
                size="small"
                class="purchase-progress-select"
                placeholder="请选择采购进度"
                @change="(value) => handlePurchaseProgressChange(record, value)"
              >
                <a-option value=""></a-option>
                <a-option value="待合同回传">待合同回传</a-option>
                <a-option value="待付款">待付款</a-option>
                <a-option value="待下单">待下单</a-option>
                <a-option value="无货/停产">无货/停产</a-option>
                <a-option value="待参数确定">待参数确定</a-option>
                <a-option value="货期">货期</a-option>
                <a-option value="待废止">待废止</a-option>
                <a-option value="待确认价格">待确认价格</a-option>
              </a-select>
              <!-- 货期时间输入框 - 当选择"货期"时显示 -->
              <div
                v-if="record.purchaseProgress === '货期'"
                class="delivery-time-wrapper"
              >
                <!-- <a-input
                  :model-value="record.deliveryTime"
                  size="small"
                  class="delivery-time-input"
                  placeholder="请填写货期时间"
                  @blur="
                    (e) => handleDeliveryTimeChange(record, e.target.value)
                  "
                  @press-enter="
                    (e) => handleDeliveryTimeChange(record, e.target.value)
                  "
                /> -->
                <a-date-picker
                  :model-value="record.deliveryTime"
                  show-time
                  style="width: 220px; margin: 0 24px 24px 0"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="timestamp"
                  @change="(value) => handleDeliveryTimeChange(record, value)"
                />
              </div>
            </div>
          </div>
          <div class="purchase-item">
            <span class="purchase-label">物流单号：</span>
            <span class="purchase-content">{{
              record.logisticsNumber || "-"
            }}</span>
          </div>
        </div>
      </template>

      <!-- 备注信息插槽 -->
      <template #remarkInfo="{ record }">
        <div class="remark-info-card">
          <div class="remark-item" v-if="record.businessRemark">
            <span class="remark-label">业务员备注：</span>
            <span class="remark-content" style="white-space: pre-wrap">{{
              record.businessRemark
            }}</span>
          </div>
          <div class="remark-item" v-if="record.purchaseRemark">
            <span class="remark-label">采购备注：</span>
            <span class="remark-content" style="white-space: pre-wrap">{{
              record.purchaseRemark
            }}</span>
          </div>
          <div
            class="remark-item"
            v-if="!record.businessRemark && !record.purchaseRemark"
          >
            <span class="remark-content text-gray" style="white-space: pre-wrap"
              >暂无备注</span
            >
          </div>
        </div>
      </template>

      <!-- 状态信息插槽 -->
      <template #statusInfo="{ record }">
        <div class="status-info-card">
          <div class="status-item" v-if="record.originOrderInfo">
            <span class="status-label">订单状态：</span>
            <a-tag
              :color="
                getOriginOrderStatusColor(record.originOrderInfo.orderStatus)
              "
            >
              {{ getOriginOrderStatusText(record.originOrderInfo.orderStatus) }}
            </a-tag>
          </div>
          <div class="status-item">
            <span class="status-label">审核状态：</span>
            <a-tag :color="getAuditStatusColor(record.auditStatus)">
              {{ record.auditStatus }}
            </a-tag>
          </div>
          <div class="status-item">
            <span class="status-label">ERP状态：</span>
            <a-tag :color="getErpStatusColor(record.erpStatus)">
              {{ record.erpStatus }}
            </a-tag>
          </div>
          <div class="status-item">
            <span class="status-label">拆分状态：</span>
            <a-tag :color="getSplitOrderStatusColor(record.splitOrderStatus)">
              {{ record.splitOrderStatus }}
            </a-tag>
          </div>
          <div class="status-item" v-if="record.cancelStatus">
            <span class="status-label">废止状态：</span>
            <a-tag :color="getCancelStatusColor(record.cancelStatus)">
              {{ record.cancelStatus }}
            </a-tag>
          </div>
          <div class="status-item" v-if="record.pendingCancelStatus">
            <span class="status-label">待废止状态：</span>
            <a-tag
              :color="getPendingCancelStatusColor(record.pendingCancelStatus)"
            >
              {{ record.pendingCancelStatus }}
            </a-tag>
          </div>
        </div>
      </template>

      <!-- 操作列插槽 -->
      <template #operation="{ record }">
        <a-space direction="vertical" fill>
          <!-- 查看详情 - 始终显示 -->
          <a-button type="text" size="small" @click="handleViewDetail(record)">
            <template #icon><icon-eye /></template>
            查看详情
          </a-button>

          <!-- 当待废止状态为"采购员待审核"时，只显示废止操作和废止记录 -->
          <!-- <template v-if="record.pendingCancelStatus === '采购员待审核'">
            <a-button type="text" size="small" @click="handleAbolishOperation(record)">
              <template #icon><icon-close /></template>
              废止操作
            </a-button>
            <a-button type="text" size="small" @click="handleAbolishRecord(record)">
              <template #icon><icon-history /></template>
              废止记录
            </a-button>
          </template> -->

          <!-- 其他状态下显示所有操作按钮 -->

          <a-button
            type="text"
            size="small"
            @click="handleOrderTemplate(record)"
          >
            <template #icon><icon-file-text /></template>
            下单模版
          </a-button>
          <a-button
            type="text"
            size="small"
            @click="handleChangePurchaser(record)"
          >
            <template #icon><icon-user /></template>
            更换采购员
          </a-button>
          <a-button
            type="text"
            size="small"
            @click="handlePurchaseRemark(record)"
          >
            <template #icon><icon-edit /></template>
            采购备注
          </a-button>
          <a-button
            type="text"
            size="small"
            @click="handleSplitProduct(record)"
          >
            <template #icon><icon-branch /></template>
            拆分商品
          </a-button>
          <a-button
            type="text"
            size="small"
            @click="handleExpenseOrder(record)"
          >
            <template #icon><icon-file /></template>
            费用单
          </a-button>
          <!-- 根据链接状态显示不同的链接操作 -->
          <a-button
            v-if="!record.linkGenerated || record.linkStatus === 'unlinked'"
            type="text"
            size="small"
            @click="handleGenerateLink(record)"
            class="generate-link-btn"
          >
            <template #icon><icon-plus /></template>
            生成链接
          </a-button>
          <a-button
            v-else-if="
              record.linkGenerated || record.linkStatus === 'linked'
            "
            type="text"
            size="small"
            @click="handleCopyLink(record)"
            class="copy-link-btn"
          >
            <template #icon><icon-link /></template>
            复制链接
          </a-button>
        </a-space>
      </template>
    </ma-crud>

    <!-- 供应商选择弹窗 -->
    <SupplierSelectModal
      v-model:visible="supplierSelectVisible"
      :order-data="currentOrderData"
      @confirm="handleSupplierConfirm"
    />
    <SupplierAndinquirySelectModal
      v-model:visible="supplierSelectVisible"
      :order-data="currentOrderData"
      @confirm="handleSupplierConfirm"
    />

    <!-- 税收分类选择弹窗 -->
    <TaxClassificationModal
      v-model:visible="taxClassificationVisible"
      :product-data="currentProductData"
      @confirm="handleTaxClassificationConfirm"
    />

    <!-- 生成链接确认弹窗 -->
    <a-modal
      v-model:visible="generateLinkVisible"
      title="生成链接"
      :width="450"
      :footer="false"
      @cancel="handleGenerateLinkCancel"
      class="generate-link-modal"
    >
      <div class="generate-link-content">
        <div class="confirm-question">是否确认该订单生成链接？</div>

        <div class="form-container">
          <div class="form-row">
            <span class="form-label">
              <span class="required-star">*</span>
              实际收货人：
            </span>
            <a-input
              v-model="generateLinkForm.recipientName"
              placeholder="213"
              class="form-control"
            />
          </div>

          <div class="form-row">
            <span class="form-label">
              <span class="required-star">*</span>
              实际收货电话：
            </span>
            <a-input
              v-model="generateLinkForm.contactPhone"
              placeholder="23"
              class="form-control"
            />
          </div>

          <div class="form-row">
            <span class="form-label">
              <span class="required-star">*</span>
              实际收货地址：
            </span>
            <a-textarea
              v-model="generateLinkForm.deliveryAddress"
              placeholder="内蒙古通辽市科尔沁左翼中旗33-23"
              class="form-control form-textarea"
              :rows="3"
              :auto-size="{ minRows: 3, maxRows: 5 }"
            />
          </div>
        </div>

        <div class="button-group">
          <a-button @click="handleGenerateLinkCancel" class="cancel-btn"
            >取消</a-button
          >
          <a-button
            type="primary"
            @click="handleConfirmGenerateLink"
            class="confirm-btn"
            :disabled="!isGenerateLinkFormValid"
          >
            确认
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 复制链接弹窗 -->
    <a-modal
      v-model:visible="copyLinkVisible"
      title="复制链接"
      :width="500"
      :footer="false"
      @cancel="handleCopyLinkCancel"
    >
      <div class="copy-link-content">
        <!-- 订单信息 -->
        <div class="order-info-section">
          <div class="info-item">
            <span class="info-label">采购订单号：</span>
            <span class="info-value">{{
              currentCopyOrder?.shippingLinkData?.orderInfo?.purchaseOrderNumber || currentCopyOrder?.orderNumber || ""
            }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">原订单号：</span>
            <span class="info-value">{{
              currentCopyOrder?.shippingLinkData?.orderInfo?.originalOrderNumber || ""
            }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">供应商：</span>
            <span class="info-value">{{
              currentCopyOrder?.shippingLinkData?.orderInfo?.supplierName || currentCopyOrder?.supplierName || ""
            }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">发货二维码：</span>
          </div>
        </div>

        <!-- 二维码区域 -->
        <div class="qrcode-section">
          <div class="qrcode-container">
            <div class="qrcode-placeholder">
              <!-- 显示真实的二维码图片 -->
              <div v-if="currentCopyOrder?.qrCodeUrl" class="qrcode-image">
                <img
                  :src="currentCopyOrder.qrCodeUrl"
                  alt="发货二维码"
                  class="qrcode-img"
                  @error="handleQrCodeError"
                />
              </div>
              <!-- 如果没有二维码，显示占位符 -->
              <div v-else class="qrcode-mock">
                <div class="qrcode-grid">
                  <div
                    v-for="i in 225"
                    :key="i"
                    class="qrcode-dot"
                    :class="{ active: Math.random() > 0.5 }"
                  ></div>
                </div>
              </div>
            </div>
            <div class="qrcode-info">
              <div class="qrcode-order-number">
                采购订单号：{{ currentCopyOrder?.shippingLinkData?.orderInfo?.purchaseOrderNumber || currentCopyOrder?.orderNumber || "" }}
              </div>
              <a-button
                type="primary"
                size="small"
                class="copy-order-btn"
                @click="copyOrderNumber"
              >
                复制订单号
              </a-button>
            </div>
          </div>

          <!-- 订单详细信息 -->
          <div class="order-details">
            <div class="detail-item">
              <span class="detail-label">收货人：</span>
              <span class="detail-value">{{
                currentCopyOrder?.recipientName || currentCopyOrder?.shippingLinkData?.recipientName || ""
              }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">收货电话：</span>
              <span class="detail-value">{{
                currentCopyOrder?.recipientPhone || currentCopyOrder?.shippingLinkData?.recipientPhone || ""
              }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">收货地址：</span>
              <span class="detail-value">{{
                currentCopyOrder?.recipientAddress || currentCopyOrder?.shippingLinkData?.recipientAddress || currentCopyOrder?.deliveryAddress || ""
              }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">生成时间：</span>
              <span class="detail-value">{{
                currentCopyOrder?.generatedAt || currentCopyOrder?.shippingLinkData?.generatedAt ?
                formatDateTime(currentCopyOrder?.generatedAt || currentCopyOrder?.shippingLinkData?.generatedAt) : ""
              }}</span>
            </div>
          </div>
        </div>

        <!-- 链接区域 -->
        <div class="link-section">
          <div class="link-label">文本链接：</div>
          <div class="link-input-wrapper">
            <a-input
              :model-value="generateOrderLink(currentCopyOrder)"
              readonly
              class="link-input"
            />
            <a-button type="primary" @click="copyOrderLink">
              复制链接
            </a-button>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="modal-footer">
          <a-button @click="handleCopyLinkCancel">取消</a-button>
        </div>
      </div>
    </a-modal>

    <!-- 费用单弹窗已移至主页面处理 -->

    <!-- 下单模版弹窗 -->
    <OrderTemplateModal
      :model-value="orderTemplateVisible"
      :order-data="currentTemplateOrder"
      @update:model-value="orderTemplateVisible = $event"
    />

    <!-- 废止操作弹窗 -->
    <a-modal
      v-model:visible="abolishOperationVisible"
      title="订单废止"
      :width="500"
      :footer="false"
      @cancel="handleAbolishOperationCancel"
    >
      <div class="abolish-operation-content">
        <div class="order-info">
          <div class="info-item">
            <span class="info-label">订单号：</span>
            <span class="info-value">{{
              currentAbolishOrder?.orderNumber || ""
            }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">订单来源：</span>
            <span class="info-value">{{
              currentAbolishOrder?.orderSource || ""
            }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">废止原因：</span>
            <span class="info-value">{{
              currentAbolishOrder?.abolishReason || "订单拍错/重复下单"
            }}</span>
          </div>
        </div>

        <div class="form-section">
          <div class="form-item">
            <span class="form-label required">同意废止：</span>
            <a-radio-group v-model="abolishForm.agree" class="radio-group">
              <a-radio :value="true" class="agree-radio">是</a-radio>
              <a-radio :value="false" class="disagree-radio">否</a-radio>
            </a-radio-group>
          </div>

          <div class="form-item">
            <span class="form-label">审核备注：</span>
            <a-textarea
              v-model="abolishForm.remark"
              placeholder="请输入审核备注"
              :rows="3"
              :max-length="200"
              show-word-limit
              class="remark-textarea"
            />
          </div>
        </div>

        <div class="warning-tip">
          <icon-exclamation-circle-fill class="warning-icon" />
          <span class="warning-text">温馨提示：同意废止后，注意订单的跟进</span>
        </div>

        <div class="button-group">
          <a-button @click="handleAbolishOperationCancel" class="cancel-btn"
            >取消</a-button
          >
          <a-button
            type="primary"
            @click="handleConfirmAbolishOperation"
            class="confirm-btn"
          >
            确定
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 废止记录弹窗 -->
    <a-modal
      v-model:visible="abolishRecordVisible"
      title="废止记录"
      :width="800"
      :footer="false"
      @cancel="handleAbolishRecordCancel"
    >
      <div class="abolish-record-content">
        <a-table
          :data="abolishRecordData"
          :pagination="false"
          :bordered="true"
          size="small"
        >
          <template #columns>
            <a-table-column
              title="序号"
              data-index="id"
              :width="60"
              align="center"
            />
            <a-table-column title="操作" data-index="operation" :width="120" />
            <a-table-column
              title="操作账号"
              data-index="operator"
              :width="100"
            />
            <a-table-column title="废止原因" data-index="reason" :width="150" />
            <a-table-column title="审核备注" data-index="remark" :width="150" />
            <a-table-column
              title="操作时间"
              data-index="operationTime"
              :width="160"
            >
              <template #cell="{ record }">
                {{ formatDateTime(record.operationTime) }}
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<style scoped>
.order-table {
  height: 100%;
}

/* 过期产品线行样式 */
:deep(.expired-row) {
  background-color: #fff2f0 !important;
}

:deep(.expired-row:hover) {
  background-color: #ffebe6 !important;
}

/* 产品线过期时间显示样式 */
.expire-time-display.expired {
  color: #ff4d4f;
  font-weight: bold;
}

.expire-time-display.expiring {
  color: #fa8c16;
  font-weight: bold;
}

.expire-time-display.valid {
  color: #52c41a;
}

/* 优化固定表格的滚动条样式 */
:deep(.arco-table-body) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

:deep(.arco-table-body::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.arco-table-body::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.arco-table-body::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.arco-table-body::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 确保表格容器高度正确 */
:deep(.ma-crud .arco-table-container) {
  height: 100%;
}

:deep(.ma-crud .arco-table) {
  height: 100%;
}
</style>

<script setup>
import { ref, reactive, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import SupplierAndinquirySelectModal from "../SupplierAndinquirySelectModal/index.vue";
import TaxClassificationModal from "../TaxClassificationModal/index.vue";
// import ExpenseOrderModal from '../ExpenseOrderModal/index.vue' // 已移至主页面
import OrderTemplateModal from "../OrderTemplateModal/index.vue";
import orderApi from "~/api/master/order";
import { purchaseOrderApi } from "~/api/master/csm/purchaseOrder";
import { OrderTypeEnum } from "~/constants/OrderTypeEnum";

// 移除props定义，因为现在使用ma-crud的api方式
// const props = defineProps({})

const emit = defineEmits([
  "view-detail",
  "order-template",
  "change-purchaser",
  "purchase-remark",
  "split-product",
  "expense-order",
  "expense-order-created",
  "request",
  "supplier-updated",
  "tax-classification-updated",
  "loss-reason-updated",
  "cost-total-updated",
  "purchase-progress-updated",
  "delivery-time-updated",
  "copy-link",
  "generate-link",
  "export",
  "abolish-operation",
  "abolish-record",
  "view-product-detail",
]);

const crudRef = ref();
const crudBinding = ref({});

// 供应商选择相关
const supplierSelectVisible = ref(false);
const currentOrderData = ref({});

// 税收分类选择相关
const taxClassificationVisible = ref(false);
const currentProductData = ref({});
const currentOrderForTax = ref({});

// 生成链接相关
const generateLinkVisible = ref(false);
const currentGenerateOrder = ref({});
const generateLinkForm = reactive({
  recipientName: "",
  contactPhone: "",
  deliveryAddress: "",
});

// 复制链接相关
const copyLinkVisible = ref(false);
const currentCopyOrder = ref({});

// 费用单相关逻辑已移至主页面

// 下单模版相关
const orderTemplateVisible = ref(false);
const currentTemplateOrder = ref({});

// 废止操作相关
const abolishOperationVisible = ref(false);
const currentAbolishOrder = ref({});

// 废止记录相关
const abolishRecordVisible = ref(false);
const currentRecordOrder = ref({});

// 废止操作表单
const abolishForm = reactive({
  agree: true,
  remark: "",
});

// 废止记录模拟数据
const abolishRecordData = ref([
  {
    id: 1,
    operation: "跟单员-同意废止",
    operator: "黄博佳",
    reason: "订单拍错/重复下单",
    remark: "111",
    operationTime: "2024-05-14 17:56:05",
  },
  {
    id: 2,
    operation: "中控台-申请废止",
    operator: "testAdmin",
    reason: "订单拍错/重复下单",
    remark: "",
    operationTime: "2024-05-14 17:55:51",
  },
]);

// 生成链接表单验证
const isGenerateLinkFormValid = computed(() => {
  return (
    generateLinkForm.recipientName.trim() !== "" &&
    generateLinkForm.contactPhone.trim() !== "" &&
    generateLinkForm.deliveryAddress.trim() !== ""
  );
});
// 表格配置
const crudOptions = reactive({
  // 启用固定布局，实现固定表格高度和底部滚动条
  pageLayout: "fixed",

  // 表格滚动配置
  scroll: {
    x: "100%", // 水平滚动
    y: "100%", // 垂直滚动
  },

  // 表头吸顶
  stickyHeader: true,

  // 启用tabs功能 - 根据后端PURCHASE_STATUS常量配置
  tabs: {
    type: "line",
    trigger: "click",
    dataIndex: "purchaseStatus",
    data: [
      { label: "全部", value: "" },
      { label: "待处理", value: 0 }, // PURCHASE_STATUS.PENDING
      { label: "已分配", value: 1 }, // PURCHASE_STATUS.ASSIGNED
      { label: "采购中", value: 2 }, // PURCHASE_STATUS.PURCHASING
      { label: "已完成", value: 3 }, // PURCHASE_STATUS.COMPLETED
      { label: "已取消", value: 4 }, // PURCHASE_STATUS.CANCELLED
    ],
    defaultKey: "",
    searchKey: "purchaseStatus",
  },
  showSummary: true,
  // 使用api配置进行数据请求
  api: async (params) => {
    try {
      console.log("ma-crud请求参数:", params);

      // 处理tabs切换的状态参数
      const processedParams = { ...params };

      // 如果有purchaseStatus参数，确保正确传递给后端
      if (params.purchaseStatus !== undefined && params.purchaseStatus !== "") {
        processedParams.purchaseStatus = params.purchaseStatus;
      }

      // 触发父组件的请求事件，并等待返回结果
      const result = await new Promise((resolve) => {
        emit("request", processedParams, resolve);
      });

      console.log("ma-crud接收到的结果:", result);

      // 返回ma-crud期望的原始API响应格式，让config.parseResponseData自动解析
      return result;
    } catch (error) {
      console.error("ma-crud API请求失败:", error);
      return {
        code: 500,
        message: "请求失败",
        data: {
          items: [],
          pageInfo: {
            total: 0,
            currentPage: 1,
            totalPage: 1,
          },
        },
      };
    }
  },
  showIndex: false,
  tablePagination: false,
  operationColumn: false,
  // 分页配置
  pageSize: 10,
  pageSizeOption: [10, 20, 30, 50, 100],
  searchLabelWidth: "100px",
  searchColNumber: 4,
  rowSelection: false,
  add: false,
  edit: false,
  delete: false,
  viewLayoutSetting: true,
  showTools: true,
  searchSpan: 6,
  // 启用搜索功能
  search: true,
  searchShow: true,
  searchCollapse: true,
  searchReset: true,
  searchSubmit: true,
  // 导出功能配置
  export: {
    show: true,
  },
  // 行样式配置
  rowClass: (record) => {
    return hasExpiredProductLine(record) ? "expired-row" : "";
  },
});

// 列配置
const columns = [
  // 搜索字段配置
  {
    title: "链接状态",
    dataIndex: "linkStatus",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "未链接", value: 0 }, // LINK_STATUS.UNLINKED
        { label: "已链接", value: 1 }, // LINK_STATUS.LINKED
      ],
    },
    searchPlaceholder: "请选择链接状态",
  },
  {
    title: "订单编号",
    dataIndex: "originalOrderNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入原订单编号",
  },
  {
    title: "渠道",
    dataIndex: "channelId",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: async () => {
        try {
          const result = await orderApi.getChannelList({ pageSize: 100 });
          if (result.success && result.data && result.data.items) {
            return [
              { label: "全部", value: "" },
              ...result.data.items.map((item) => ({
                label: item.name,
                value: item.id,
              })),
            ];
          }
          return [{ label: "全部", value: "" }];
        } catch (error) {
          console.error("获取渠道列表失败:", error);
          return [{ label: "全部", value: "" }];
        }
      },
    },
    searchPlaceholder: "请选择订单来源",
  },
  {
    title: "跟单员",
    dataIndex: "follower",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入跟单员",
  },
  {
    title: "采购员",
    dataIndex: "purchaser",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入采购员",
  },
  {
    title: "订单状态",
    dataIndex: "orderStatus",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "待付款", value: 0 }, // OrderStatusEnum.PENDING_PAYMENT
        { label: "待发货", value: 1 }, // OrderStatusEnum.PENDING_SHIPMENT
        { label: "已发货", value: 2 }, // OrderStatusEnum.SHIPPED
        { label: "交易成功", value: 3 }, // OrderStatusEnum.COMPLETED
        { label: "已关闭", value: 4 }, // OrderStatusEnum.CANCELLED
        { label: "退款中", value: 5 }, // OrderStatusEnum.REFUNDING
      ],
    },
    searchPlaceholder: "请选择订单状态",
  },
  {
    title: "审核状态",
    dataIndex: "auditStatus",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "待审核", value: 0 }, // AUDIT_STATUS.PENDING_AUDIT
        { label: "审核通过", value: 1 }, // AUDIT_STATUS.AUDIT_PASSED
        { label: "审核驳回", value: 2 }, // AUDIT_STATUS.AUDIT_REJECTED
        { label: "废止待确认", value: 3 }, // AUDIT_STATUS.PENDING_ABOLISH_CONFIRM
      ],
    },
    searchPlaceholder: "请选择审核状态",
  },
  {
    title: "收件人姓名",
    dataIndex: "recipientName",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入收件人姓名",
  },
  {
    title: "联系电话",
    dataIndex: "contactPhone",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入联系电话",
  },
  {
    title: "商品编码",
    dataIndex: "productCode",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入商品编码",
  },
  {
    title: "商品名称",
    dataIndex: "productName",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入商品名称",
  },
  {
    title: "物流单号",
    dataIndex: "logisticsNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入物流单号",
  },
  {
    title: "订单地址",
    dataIndex: "orderAddress",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入收货地址",
  },
  {
    title: "采购备注",
    dataIndex: "purchaseRemark",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入采购备注",
  },
  {
    title: "采购时间",
    dataIndex: "purchaseTimeRange",
    formType: "range",
    search: true,
    hide: true,
    searchPlaceholder: "请选择采购时间范围",
  },
  {
    title: "成本价范围",
    dataIndex: "costPrice",
    formType: "input-number",
    rangeSearch: true,
    search: true,
    hide: true,
    searchPlaceholder: "请输入成本价范围",
  },
  {
    title: "订单类型",
    dataIndex: "originOrderInfo.orderType",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: OrderTypeEnum.getAllOptions(),
    },
    searchPlaceholder: "请选择订单类型",
  },
  {
    title: "是否亏损",
    dataIndex: "isLoss",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "否", value: 0 }, // IS_LOSS.NO
        { label: "是", value: 1 }, // IS_LOSS.YES
      ],
    },
    searchPlaceholder: "请选择是否亏损",
  },
  {
    title: "物流情况",
    dataIndex: "logisticsStatus",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "物流-暂无", value: 1 },
        { label: "物流-正常", value: 2 },
        { label: "物流-异常", value: 3 },
        { label: "物流-厂家自送", value: 4 },
      ],
    },
    searchPlaceholder: "请选择物流情况",
  },
  // 显示列配置
  {
    title: "订单信息",
    dataIndex: "orderInfo",
    width: 280,
    slot: true,
    search: false,
  },
  {
    title: "商品信息",
    dataIndex: "productInfo",
    width: 600,
    slot: true,
    search: false,
  },
  {
    title: "采购成本信息",
    dataIndex: "costInfo",
    width: 320,
    slot: true,
    search: false,
  },
  {
    title: "采购信息",
    dataIndex: "purchaseInfo",
    width: 280,
    slot: true,
    search: false,
  },
  {
    title: "状态信息",
    dataIndex: "statusInfo",
    width: 200,
    slot: true,
    search: false,
  },
  {
    title: "实际/建议供应商",
    dataIndex: "supplierInfo",
    width: 180,
    hide: true,
    search: false,
  },
  {
    title: "备注",
    dataIndex: "remarkInfo",
    width: 250,
    slot: true,
    search: false,
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 180,
    slot: true,
    search: false,
    fixed: "right",
  },
];

// 移除tableData computed，因为现在使用ma-crud的api方式
// const tableData = computed(() => [])

// 计算商品总额
const calculateProductsTotal = (record) => {
  if (!record.products || record.products.length === 0) {
    return "0.00";
  }

  const total = record.products.reduce((sum, product) => {
    return sum + parseFloat(product.totalAmount || 0);
  }, 0);

  return total.toFixed(2);
};

// 判断是否为亏损订单
const isLossOrder = (record) => {
  const costTotal = parseFloat(record.costTotal || 0);
  const inspectionCost = getTotalInspectionCost(record);
  return costTotal > inspectionCost;
};

// 计算亏损金额
const calculateLossAmount = (record) => {
  const costTotal = parseFloat(record.costTotal || 0);
  const inspectionCost = getTotalInspectionCost(record);
  const lossAmount = costTotal - inspectionCost;
  return lossAmount > 0 ? lossAmount.toFixed(2) : "0.00";
};

// 计算总巡检成本
const getTotalInspectionCost = (record) => {
  if (record.products && record.products.length > 0) {
    const total = record.products.reduce((sum, product) => {
      return sum + parseFloat(product.inspectionCost || 0);
    }, 0);
    return total;
  }
  return parseFloat(record.inspectionCost || 0);
};

// 判断产品线是否已过期
const isProductLineExpired = (expireTime) => {
  if (!expireTime) return false;
  const now = new Date();
  const expire = new Date(expireTime);
  return expire < now;
};

// 判断产品线是否即将过期（30天内）
const isProductLineExpiring = (expireTime) => {
  if (!expireTime) return false;
  const now = new Date();
  const expire = new Date(expireTime);
  const diffTime = expire - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays > 0 && diffDays <= 30;
};

// 格式化时间显示为 YYYY-MM-DD HH:mm:ss
const formatDateTime = (dateTime) => {
  if (!dateTime) return "-";

  const date = new Date(dateTime);

  // 检查是否为有效日期
  if (isNaN(date.getTime())) return "-";

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 格式化过期时间显示
const formatExpireTime = (expireTime) => {
  if (!expireTime) return "-";
  const date = new Date(expireTime);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
};

// 获取第一个产品线过期时间
const getFirstProductLineExpireTime = (record) => {
  // 处理多商品订单
  if (record.products && record.products.length > 0) {
    for (const product of record.products) {
      if (product.productLineExpireTime) {
        return formatExpireTime(product.productLineExpireTime);
      }
    }
  }
  // 处理单商品订单
  else if (record.productLineExpireTime) {
    return formatExpireTime(record.productLineExpireTime);
  }

  return "-";
};

// 判断订单是否包含过期的产品线
const hasExpiredProductLine = (record) => {
  // 处理多商品订单
  if (record.products && record.products.length > 0) {
    return record.products.some(
      (product) =>
        product.productLineExpireTime &&
        isProductLineExpired(product.productLineExpireTime)
    );
  }
  // 处理单商品订单
  else if (record.productLineExpireTime) {
    return isProductLineExpired(record.productLineExpireTime);
  }

  return false;
};

// 获取产品线过期时间的显示样式类
const getProductLineExpireDisplayClass = (record) => {
  // 处理多商品订单
  if (record.products && record.products.length > 0) {
    for (const product of record.products) {
      if (product.productLineExpireTime) {
        if (isProductLineExpired(product.productLineExpireTime)) {
          return "expired";
        } else if (isProductLineExpiring(product.productLineExpireTime)) {
          return "expiring";
        }
        return "valid";
      }
    }
  }
  // 处理单商品订单
  else if (record.productLineExpireTime) {
    if (isProductLineExpired(record.productLineExpireTime)) {
      return "expired";
    } else if (isProductLineExpiring(record.productLineExpireTime)) {
      return "expiring";
    }
    return "valid";
  }

  return "";
};

// 处理亏损原因变更
const handleLossReasonChange = (record, reason) => {
  emit("loss-reason-updated", {
    orderData: record,
    lossReason: reason,
  });
};

// 处理成本总价输入
const handleCostTotalInput = (record, value) => {
  // 实时更新数据，不进行验证
  record.costTotal = value;
};

// 处理成本总价变更
const handleCostTotalChange = (record, value) => {
  // 验证输入值
  const numValue = parseFloat(value);
  if (isNaN(numValue) || numValue < 0) {
    Message.warning("请输入有效的成本总价");
    return;
  }

  const formattedValue = numValue.toFixed(2);
  record.costTotal = formattedValue;
  emit("cost-total-updated", {
    orderData: record,
    costTotal: formattedValue,
  });
};

// 处理商品成本总价输入
const handleProductCostTotalInput = (product, value) => {
  // 实时更新数据，不进行验证
  product.costTotal = value;
};

// 处理商品成本总价变更
const handleProductCostTotalChange = (product, order, value) => {
  // 验证输入值
  const numValue = parseFloat(value);
  if (isNaN(numValue) || numValue < 0) {
    Message.warning("请输入有效的成本总价");
    return;
  }

  const formattedValue = numValue.toFixed(2);
  product.costTotal = formattedValue;
  emit("product-cost-total-updated", {
    productData: product,
    orderData: order,
    costTotal: formattedValue,
  });
};

// 判断商品是否亏损
const isProductLoss = (product) => {
  const costTotal = parseFloat(product.costTotal || 0);
  const inspectionCost = parseFloat(product.inspectionCost || 0);
  return costTotal > inspectionCost;
};

// 计算商品亏损金额
const calculateProductLossAmount = (product) => {
  const costTotal = parseFloat(product.costTotal || 0);
  const inspectionCost = parseFloat(product.inspectionCost || 0);
  const lossAmount = costTotal - inspectionCost;
  return lossAmount > 0 ? lossAmount.toFixed(2) : "0.00";
};

// 处理商品亏损原因变更
const handleProductLossReasonChange = (product, order, reason) => {
  product.lossReason = reason;
  emit("product-loss-reason-updated", {
    productData: product,
    orderData: order,
    lossReason: reason,
  });
};

// 处理采购进度变更
const handlePurchaseProgressChange = (record, progress) => {
  record.purchaseProgress = progress;

  // 如果不是选择"货期"，清空货期时间
  if (progress !== "货期") {
    record.deliveryTime = "";
  }

  emit("purchase-progress-updated", {
    orderData: record,
    purchaseProgress: progress,
  });
};

// 处理货期时间变更
const handleDeliveryTimeChange = async (record, time) => {
  if (!time) {
    return;
  }

  try {
    // 调用货期时间设置API
    await purchaseOrderApi.setExpectedDeliveryTime(record.id, {
      expected_delivery_time: time.toString(),
    });

    // 更新本地数据
    record.deliveryTime = time;

    // 发送事件通知
    emit("delivery-time-updated", {
      orderData: record,
      deliveryTime: time,
    });
  } catch (error) {
    console.error("设置货期时间失败:", error);
    Message.error(error.message || "设置货期时间失败");
  }
};

// 请求处理
const handleRequest = (params) => {
  emit("request", params);
};

// 操作事件处理
const handleViewDetail = (record) => {
  emit("view-detail", record);
};

const handleOrderTemplate = (record) => {
  currentTemplateOrder.value = record;
  orderTemplateVisible.value = true;
};

const handleChangePurchaser = (record) => {
  emit("change-purchaser", record);
};

const handlePurchaseRemark = (record) => {
  emit("purchase-remark", record);
};

const handleSplitProduct = (record) => {
  emit("split-product", record);
};

const handleExpenseOrder = (record) => {
  // 触发事件给父组件处理，而不是在这里直接打开弹窗
  emit("expense-order", record);
};

const handleAbolishOperation = (record) => {
  currentAbolishOrder.value = record;
  abolishOperationVisible.value = true;
  emit("abolish-operation", record);
};

const handleAbolishRecord = (record) => {
  currentRecordOrder.value = record;
  abolishRecordVisible.value = true;
  emit("abolish-record", record);
};

const handleCopyLink = async (record) => {
  try {
    // 显示加载状态
    Message.loading('正在获取发货链接信息...', 0);

    // 请求发货链接数据
    const response = await purchaseOrderApi.getShippingLink(record.id);

    // 关闭加载状态
    Message.clear();

    if (response.code === 200) {
      // 将获取到的发货链接数据合并到订单记录中
      currentCopyOrder.value = {
        ...record,
        shippingLinkData: response.data, // 保存完整的发货链接数据
        shippingLink: response.data.shippingLink,
        qrCodeUrl: response.data.qrCodeUrl,
        recipientName: response.data.recipientName,
        recipientPhone: response.data.recipientPhone,
        recipientAddress: response.data.recipientAddress,
        shippingType: response.data.shippingType,
        generatedAt: response.data.generatedAt,
      };

      copyLinkVisible.value = true;
      emit("copy-link", currentCopyOrder.value);
    } else {
      Message.error(response.message || '获取发货链接信息失败');
    }
  } catch (error) {
    Message.clear();
    console.error('获取发货链接信息失败:', error);
    Message.error('获取发货链接信息失败，请稍后重试');
  }
};

// 检查订单中所有商品是否都已选择税收分类
const hasAllTaxClassifications = (record) => {
  if (!record) return false;

  console.log("检查税收分类:", record);

  // 如果是多商品订单
  if (
    record.products &&
    Array.isArray(record.products) &&
    record.products.length > 0
  ) {
    return record.products.every((product) => product.taxClassification?.label);
  }

  // 如果是单商品订单
  return !!record.taxClassification?.label;
};

// 生成链接处理
const handleGenerateLink = (record) => {
  // 检查是否所有商品都已选择税收分类
  if (!hasAllTaxClassifications(record)) {
    Message.error("请先选择商品税收分类");
    return;
  }

  // 所有商品都已选择税收分类，打开生成链接确认弹窗
  openGenerateLinkModal(record);
};

// 打开生成链接确认弹窗
const openGenerateLinkModal = (record) => {
  currentGenerateOrder.value = record;

  // 预填充订单信息，如果没有则使用示例数据
  generateLinkForm.recipientName = record.actualReceiver;
  generateLinkForm.contactPhone = record.actualPhone;
  generateLinkForm.deliveryAddress = record.actualAddress

  generateLinkVisible.value = true;
};

// 取消生成链接
const handleGenerateLinkCancel = () => {
  generateLinkVisible.value = false;
  currentGenerateOrder.value = {};

  // 重置表单为默认值
  generateLinkForm.recipientName = "213";
  generateLinkForm.contactPhone = "23";
  generateLinkForm.deliveryAddress = "内蒙古通辽市科尔沁左翼中旗33-23";
};

// 确认生成链接
const handleConfirmGenerateLink = async () => {
  if (!isGenerateLinkFormValid.value) {
    Message.error('请填写完整的收货信息')
    return
  }

  // 再次检查税收分类状态
  if (!hasAllTaxClassifications(currentGenerateOrder.value)) {
    Message.error('请先选择商品税收分类')
    generateLinkVisible.value = false
    return
  }

  try {
    // 调用后端API生成发货链接
    const response = await purchaseOrderApi.generateShippingLink(currentGenerateOrder.value.id, {
      recipientName: generateLinkForm.recipientName,
      recipientPhone: generateLinkForm.contactPhone,
      recipientAddress: generateLinkForm.deliveryAddress,
      shippingType: 1 // 默认快递物流
    })

    if (response.code === 200) {
      // 更新订单信息，包括链接生成状态
      const updatedOrder = {
        ...currentGenerateOrder.value,
        recipientName: generateLinkForm.recipientName,
        contactPhone: generateLinkForm.contactPhone,
        deliveryAddress: generateLinkForm.deliveryAddress,
        linkGenerated: true, // 标记链接已生成
        linkGeneratedAt: new Date().toISOString(), // 记录生成时间
        shippingLink: response.data.shippingLink, // 保存生成的链接
        qrCodeUrl: response.data.qrCodeUrl // 保存二维码图片地址
      }

      // 通知父组件生成链接
      emit('generate-link', updatedOrder)

      // 关闭弹窗
      generateLinkVisible.value = false

      Message.success('链接生成成功！')
    } else {
      Message.error(response.message || '链接生成失败')
    }
  } catch (error) {
    console.error('生成链接失败:', error)
    Message.error(error.message || '链接生成失败，请稍后重试')
  }
};

// 费用单提交逻辑已移至主页面

// 供应商选择处理
const handleSelectSupplier = (record) => {
  currentOrderData.value = record;
  supplierSelectVisible.value = true;
};

// 商品级别供应商选择处理
const handleSelectProductSupplier = (productData, orderData) => {
  // 确保传递商品的 SKU 信息
  const productSku =
    productData.sku || productData.goodsSkuId || productData.productCode;

  if (!productSku) {
    Message.error("无法获取商品SKU信息，请检查商品数据");
    return;
  }

  currentOrderData.value = {
    ...orderData,
    currentProduct: {
      ...productData,
      sku: productSku, // 确保 SKU 字段存在
    },
    // 为供应商选择组件提供商品SKU
    productSku: productSku,
  };

  supplierSelectVisible.value = true;
};

// 供应商确认选择
const handleSupplierConfirm = async ({
  supplier,
  orderData,
  quotationPrice,
}) => {
  try {
    // 检查是否是商品级别的供应商选择
    const isProductLevel =
      orderData.currentProduct && orderData.currentProduct.id;

    if (!isProductLevel) {
      Message.error("请选择具体的商品项来设置供应商");
      return;
    }

    // 更新当前商品的实际成本
    if (quotationPrice && quotationPrice > 0) {
      const currentProduct = orderData.currentProduct;
      // 更新商品的实际成本字段
      currentProduct.actualCost = quotationPrice;
      currentProduct.costTotal = (
        quotationPrice * (currentProduct.quantity || 1)
      ).toFixed(2);

      console.log("更新商品实际成本:", {
        productId: currentProduct.id,
        actualCost: quotationPrice,
        costTotal: currentProduct.costTotal,
      });
    }

    // 构建供应商数据，包含商品项ID和报价金额
    const supplierData = {
      ...supplier,
      itemId: orderData.currentProduct.id,
      quotationPrice: quotationPrice, // 添加报价金额
    };

    // 触发父组件的供应商更新事件
    emit("supplier-updated", {
      orderData: orderData,
      supplier: supplierData,
      quotationPrice: quotationPrice, // 传递报价金额给父组件
    });
  } catch (error) {
    console.error("更新供应商失败:", error);
    Message.error("更新供应商失败，请重试");
  }
};

// 税收分类选择处理
const handleSelectTaxClassification = (productData, orderData) => {
  currentProductData.value = productData;
  currentOrderForTax.value = orderData;
  taxClassificationVisible.value = true;
};

// 税收分类确认选择
const handleTaxClassificationConfirm = async ({
  taxClassification,
  productData,
}) => {
  try {
    // 这里可以调用API更新税收分类信息
    // await productApi.updateTaxClassification(productData.id, {
    //   taxClassificationId: taxClassification.codeid,
    //   taxClassificationName: taxClassification.label,
    //   taxClassificationCode: taxClassification.num
    // })

    Message.success("税收分类更新成功");
    emit("tax-classification-updated", {
      productData,
      taxClassification,
      orderData: currentOrderForTax.value,
    });
  } catch (error) {
    console.error("更新税收分类失败:", error);
    Message.error("更新税收分类失败，请重试");
  }
};

/**
 * 订单状态枚举
 * 定义订单在不同生命周期阶段的状态
 */
const OrderStatusEnum = {
  /**
   * 待付款 - 订单已创建但尚未支付
   */
  PENDING_PAYMENT: 0,

  /**
   * 待发货 - 订单已支付但尚未发货
   */
  PENDING_SHIPMENT: 1,

  /**
   * 已发货 - 订单已发货但尚未确认收货
   */
  SHIPPED: 2,

  /**
   * 交易成功 - 订单已确认收货并完成
   */
  COMPLETED: 3,

  /**
   * 已关闭 - 订单被用户或系统取消、退款完成
   */
  CANCELLED: 4,

  /**
   * 退款中 - 订单正在退款处理中
   */
  REFUNDING: 5,

  /**
   * 根据状态码获取状态名称
   * @param {number} statusCode - 状态码
   * @returns {string} - 状态名称
   */
  getStatusName(statusCode) {
    switch (parseInt(statusCode)) {
      case this.PENDING_PAYMENT:
        return "待付款";
      case this.PENDING_SHIPMENT:
        return "待发货";
      case this.SHIPPED:
        return "已发货";
      case this.COMPLETED:
        return "交易成功";
      case this.CANCELLED:
        return "已关闭";
      case this.REFUNDING:
        return "退款中";
      default:
        return "未知状态";
    }
  },
};

// 状态颜色函数
const getOrderStatusColor = (status) => {
  // 如果传入的是数字状态码，先转换为状态名称
  const statusName =
    typeof status === "number" ? OrderStatusEnum.getStatusName(status) : status;

  const colorMap = {
    待付款: "orange",
    待发货: "blue",
    已发货: "cyan",
    交易成功: "green",
    已关闭: "red",
    退款中: "magenta",
  };
  return colorMap[statusName] || "gray";
};

const getAuditStatusColor = (status) => {
  const colorMap = {
    审核通过: "green",
    待审核: "orange",
    审核驳回: "red",
    废止待确认: "magenta",
  };
  return colorMap[status] || "gray";
};

const getErpStatusColor = (status) => {
  const colorMap = {
    已同步: "green",
    待同步: "orange",
    同步失败: "red",
  };
  return colorMap[status] || "gray";
};

const getSplitOrderStatusColor = (status) => {
  const colorMap = {
    未拆分: "blue",
    已拆分: "green",
    拆分中: "orange",
  };
  return colorMap[status] || "gray";
};

const getCancelStatusColor = (status) => {
  if (!status) return "gray";
  const colorMap = {
    待废止: "orange",
    废止待确认: "red",
    已废止: "gray",
  };
  return colorMap[status] || "gray";
};

const getPendingCancelStatusColor = (status) => {
  if (!status) return "gray";
  const colorMap = {
    待废止: "orange",
    废止待确认: "red",
    废止审核中: "blue",
    废止已驳回: "magenta",
  };
  return colorMap[status] || "gray";
};

// 复制链接相关方法
const handleCopyLinkCancel = () => {
  copyLinkVisible.value = false;
  currentCopyOrder.value = {};
};

// 生成订单链接
const generateOrderLink = (order) => {
  // 如果有从后端获取的发货链接数据，优先使用
  if (order?.shippingLinkData?.shippingLink) {
    return order.shippingLinkData.shippingLink;
  }

  // 如果有直接的发货链接，使用它
  if (order?.shippingLink) {
    return order.shippingLink;
  }

  // 兜底：使用原来的逻辑（但这种情况下应该不会显示复制链接按钮）
  if (!order?.orderNumber) return "";
  return `https://uat.v3.supply.8080bl.com/#/havedship?orderNumber=${order.orderNumber}`;
};

// 获取商品品牌
const getProductBrand = (order) => {
  if (!order) return "";
  if (
    order.products &&
    Array.isArray(order.products) &&
    order.products.length > 0
  ) {
    return order.products[0]?.brand || order.products[0]?.productBrand || "";
  }
  return order.brand || order.productBrand || "";
};

// 获取商品品类
const getProductCategory = (order) => {
  if (!order) return "";
  if (
    order.products &&
    Array.isArray(order.products) &&
    order.products.length > 0
  ) {
    return (
      order.products[0]?.category || order.products[0]?.productCategory || ""
    );
  }
  return order.category || order.productCategory || "";
};

// 复制订单号
const copyOrderNumber = async () => {
  try {
    const orderNumber = currentCopyOrder.value?.shippingLinkData?.orderInfo?.purchaseOrderNumber ||
                       currentCopyOrder.value?.orderNumber || "";
    if (!orderNumber) {
      Message.error("订单号为空，无法复制");
      return;
    }
    await navigator.clipboard.writeText(orderNumber);
    Message.success("订单号复制成功");
  } catch (error) {
    console.error("复制失败:", error);
    Message.error("复制失败，请手动复制");
  }
};

// 复制订单链接
const copyOrderLink = async () => {
  try {
    const link = generateOrderLink(currentCopyOrder.value);
    await navigator.clipboard.writeText(link);
    Message.success("链接复制成功");
  } catch (error) {
    console.error("复制失败:", error);
    Message.error("复制失败，请手动复制");
  }
};

// 处理二维码加载错误
const handleQrCodeError = (event) => {
  console.error("二维码图片加载失败:", event);
  // 可以在这里设置一个错误状态，显示错误提示
};

// 废止操作相关方法
const handleAbolishOperationCancel = () => {
  abolishOperationVisible.value = false;
  currentAbolishOrder.value = {};
  // 重置表单
  abolishForm.agree = true;
  abolishForm.remark = "";
};

const handleConfirmAbolishOperation = () => {
  // 这里可以调用API提交废止操作
  console.log("废止操作数据:", {
    orderId: currentAbolishOrder.value.id,
    orderNumber: currentAbolishOrder.value.orderNumber,
    agree: abolishForm.agree,
    remark: abolishForm.remark,
  });

  Message.success(abolishForm.agree ? "废止操作已同意" : "废止操作已拒绝");
  abolishOperationVisible.value = false;

  // 重置表单
  abolishForm.agree = true;
  abolishForm.remark = "";
};

// 废止记录相关方法
const handleAbolishRecordCancel = () => {
  abolishRecordVisible.value = false;
  currentRecordOrder.value = {};
};

// 处理商品图片点击
const handleProductImageClick = (productId) => {
  console.log("商品图片点击:", productId);
  emit("view-product-detail", productId);
};

// 原订单状态处理方法
const getOriginOrderStatusText = (status) => {
  const statusMap = {
    0: "待付款",
    1: "待发货",
    2: "已发货",
    3: "交易成功",
    4: "已关闭",
    5: "已退款",
  };
  return statusMap[status] || "未知状态";
};

const getOriginOrderStatusColor = (status) => {
  const colorMap = {
    0: "orange", // 待付款
    1: "blue", // 已付款待发货
    2: "cyan", // 已发货待收货
    3: "green", // 交易成功
    4: "red", // 已关闭
    5: "purple", // 已退款
  };
  return colorMap[status] || "gray";
};

// 刷新表格数据的方法
const refreshTable = () => {
  console.log("OrderTable: 开始刷新数据");
  if (crudRef.value) {
    console.log("OrderTable: 找到 crudRef，调用刷新方法");
    // 尝试多种刷新方法
    if (typeof crudRef.value.refresh === "function") {
      crudRef.value.refresh();
      console.log("OrderTable: refresh 方法调用成功");
    } else if (typeof crudRef.value.requestData === "function") {
      crudRef.value.requestData();
      console.log("OrderTable: requestData 方法调用成功");
    } else if (typeof crudRef.value.getData === "function") {
      crudRef.value.getData();
      console.log("OrderTable: getData 方法调用成功");
    } else {
      console.log("OrderTable: 未找到合适的刷新方法");
      console.log("OrderTable: crudRef 可用方法:", Object.keys(crudRef.value));
    }
  } else {
    console.log("OrderTable: crudRef 不存在");
  }
};

// 暴露组件方法和属性给父组件
defineExpose({
  crudRef,
  refreshTable,
});
</script>

<style scoped>
@import "./css/index.css";
</style>
