<template>
  <a-modal
    v-model:visible="visible"
    title="申请采购"
    width="900px"
    @cancel="handleCancel"
    class="purchase-application-modal"
  >
    <div class="modal-content">
      <a-form
        :model="formData"
        layout="vertical"
        ref="formRef"
        class="purchase-form"
      >
        <!-- 主要内容区域 -->
        <div class="form-main">
          <!-- 左侧表单 -->
          <div class="form-left">
            <a-form-item
              label="采购员"
              field="sourceSupplier"
              :rules="[{ required: true, message: '请选择采购员' }]"
              class="form-item-required"
            >
              <PurchaserSelector
                v-model="formData.sourceSupplier"
                placeholder="请选择采购员"
                value-field="id"
                :auto-load="true"
                @change="handlePurchaserChange"
                @load-success="handlePurchaserLoadSuccess"
                @load-error="handlePurchaserLoadError"
                ref="purchaserSelectorRef"
              />
            </a-form-item>

            <a-form-item
              label="实际收货人"
              field="actualReceiver"
              :rules="[{ required: true, message: '请输入实际收货人' }]"
              class="form-item-required"
            >
              <a-input
                v-model="formData.actualReceiver"
                placeholder="请输入实际收货人"
              />
            </a-form-item>

            <a-form-item
              label="联系电话"
              field="contactPhone"
              :rules="[
                { required: true, message: '请输入联系电话' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
              ]"
              class="form-item-required"
            >
              <a-input
                v-model="formData.contactPhone"
                placeholder="请输入联系电话"
              />
            </a-form-item>

            <a-form-item
              label="实际收货地址"
              field="actualAddress"
              :rules="[{ required: true, message: '请输入实际收货地址' }]"
              class="form-item-required"
            >
              <a-textarea
                v-model="formData.actualAddress"
                placeholder="请输入实际收货地址"
                :rows="2"
              />
            </a-form-item>
          </div>

          <!-- 右侧表单 -->
          <div class="form-right">
            <a-form-item
              label="业务员备注"
              field="businessManager"
            >
              <a-textarea
                v-model="formData.businessManager"
                placeholder="请输入业务员备注"
                :rows="3"
              />
            </a-form-item>

            <a-form-item
              label="订单备注"
              field="orderRemark"
            >
              <a-textarea
                v-model="formData.orderRemark"
                placeholder="请输入订单备注"
                :rows="3"
              />
            </a-form-item>

            <a-form-item
              label="客户备注"
              field="customerRemark"
            >
              <a-textarea
                v-model="formData.customerRemark"
                placeholder="客户备注"
                :rows="3"
              />
            </a-form-item>

            <a-form-item
              label="附件上传"
            >
              <div class="upload-container">
                <a-upload
                  ref="uploadRef"
                  :file-list="fileList"
                  :show-file-list="false"
                  :auto-upload="false"
                  :multiple="true"
                  :limit="5"
                  accept=".doc,.docx,.xls,.xlsx,.pdf,.png,.jpg,.jpeg"
                  @change="handleFileChange"
                  @before-upload="handleBeforeUpload"
                  class="upload-area"
                  drag
                >
                  <template #upload-button>
                    <div class="upload-button">
                      <div class="upload-icon">
                        <icon-upload />
                      </div>
                      <div class="upload-text">
                        <div class="upload-title">将文件拖拽到此处或<span class="upload-link">点击上传</span></div>
                      </div>
                    </div>
                  </template>
                </a-upload>
                <div class="upload-tip">
                  支持格式文件类型：doc、xlsx、xls、pdf、png、jpg 可上传多个，大小不超过2MB
                </div>

                <!-- 文件列表显示 -->
                <div v-if="fileList.length > 0" class="file-list">
                  <div
                    v-for="(file, index) in fileList"
                    :key="file.uid || index"
                    class="file-item"
                  >
                    <div class="file-info">
                      <icon-file class="file-icon" />
                      <div class="file-details">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="file-size">{{ formatFileSize(file.size) }}</div>
                      </div>
                    </div>
                    <div class="file-actions">
                      <a-button
                        type="text"
                        status="danger"
                        size="small"
                        @click="removeFile(index)"
                      >
                        <template #icon><icon-delete /></template>
                      </a-button>
                    </div>
                  </div>
                </div>
              </div>
            </a-form-item>
          </div>
        </div>

        <!-- 建议供应商编号表格 -->
        <div class="supplier-section">
          <a-form-item label="建议供应商编号">
            <a-table
              :columns="supplierColumns"
              :data="supplierData"
              :pagination="false"
              class="supplier-table"
              size="small"
            >
              <template #productCode="{ record }">
                <div class="flex flex-col">
                  <span>系统编码:{{ record.productCode }}</span>
                  <span>三方平台编码:{{ record.thrirdPartyCode }}</span>
                </div>
              </template>
              <template #productName="{ record }">
                <span>{{ record.productName }}</span>
              </template>
              <template #suggestedSupplier="{ record }">
                <div class="supplier-select-cell">
                  <div v-if="record.selectedSupplier" class="selected-supplier">
                    <div class="supplier-info">
                      <span class="supplier-name">{{ record.selectedSupplier.name }}</span>
                      <span class="supplier-code">({{ record.selectedSupplier.code }})</span>
                    </div>
                    <div v-if="record.selectedSupplier.quotationInfo" class="quotation-info">
                      <span class="price-tag">{{ record.selectedSupplier.quotationInfo.priceDescription }}</span>
                    </div>
                  </div>
                  <span v-else class="no-supplier">未选择</span>
                  <a-button
                    type="primary"
                    size="small"
                    @click="openSupplierSelect(record)"
                    class="select-btn"
                  >
                    选择
                  </a-button>
                </div>
              </template>
            </a-table>
          </a-form-item>
        </div>
      </a-form>
    </div>

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit">确定</a-button>
      </div>
    </template>

    <!-- 供应商选择弹窗 -->
    <SupplierAndinquirySelectModal
      v-model:visible="supplierSelectVisible"
      :order-data="currentProductRecord"
      @confirm="handleSupplierSelect"
    />
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconUpload, IconFile, IconDelete } from '@arco-design/web-vue/es/icon'
import commonApi from '@/api/common.js'
import PurchaserSelector from '~/components/master/csm/PurchaserSelector.vue'
import SupplierAndinquirySelectModal from '../../purchaseOrder/components/SupplierAndinquirySelectModal/index.vue'
import { inquiryRecordApi } from '@/api/master/csm/inquiryRecord.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)
const formRef = ref()
const uploadRef = ref()
const purchaserSelectorRef = ref()

// 供应商选择相关
const supplierSelectVisible = ref(false)
const currentProductRecord = ref(null)

// 表单数据
const formData = reactive({
  sourceSupplier: '',
  businessManager: '',
  actualReceiver: '',
  orderRemark: '',
  contactPhone: '',
  customerRemark: '',
  actualAddress: ''
})

// 供应商表格列定义
const supplierColumns = [
  {
    title: '商品编码',
    slotName: 'productCode',
    width: 220
  },
  {
    title: '商品名称',
    slotName: 'productName',
    width: 300
  },
  {
    title: '建议供应商',
    slotName: 'suggestedSupplier',
    width: 200
  }
]

// 模拟供应商数据
const supplierData = ref([
  {
    id: 1,
    productCode: '0803002409',
    thrirdPartyCode: 'TP0803002409',
    productName: '美化 半力/jdeli 7901 钢铁侠头盔 蓝牙音响',
    selectedSupplier: null
  }
])

// 文件列表
const fileList = ref([])

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetForm()
    initFormData()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 采购员选择相关事件处理
const selectedPurchaserInfo = ref(null)

const handlePurchaserChange = (data) => {
  console.log('采购员选择变化:', data)
  // 保存选中的采购员完整信息
  selectedPurchaserInfo.value = data.purchaser
  console.log('选中的采购员信息:', selectedPurchaserInfo.value)
}

const handlePurchaserLoadSuccess = (data) => {
  console.log('采购员列表加载成功:', data)
}

const handlePurchaserLoadError = (data) => {
  console.error('采购员列表加载失败:', data)
}

// 获取当前用户信息
const getCurrentUserInfo = () => {
  try {
    const userMaster = localStorage.getItem('user_master')
    if (userMaster) {
      const userData = JSON.parse(userMaster)
      console.log('当前用户信息:', userData)
      return userData
    } else {
      console.warn('未找到用户信息')
      return null
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

// 根据系统编码获取默认供应商
const getDefaultSupplierByProductCode = async (productCode) => {
  if (!productCode) {
    return null
  }

  try {
    console.log('根据系统编码获取默认供应商:', productCode)
    // 调用询价记录接口获取该SKU的供应商报价信息
    const response = await inquiryRecordApi.getSuppliersBySkuId(productCode)

    if (response.code === 200 && response.data && response.data.items && response.data.items.length > 0) {
      // 取第一条报价记录的供应商信息作为默认供应商
      const firstQuotation = response.data.items[0]
      const supplierInfo = firstQuotation.supplierInfo

      console.log('找到默认供应商:', supplierInfo)
      return {
        id: supplierInfo.id,
        code: supplierInfo.supplierCode,
        name: supplierInfo.supplierName,
        // 额外保存报价信息用于显示
        quotationInfo: {
          price: firstQuotation.currentPrice,
          priceDescription: firstQuotation.priceDescription,
          quotationId: firstQuotation.quotationId
        }
      }
    } else {
      console.log('未找到该SKU的询价记录:', productCode)
    }
  } catch (error) {
    console.error('根据系统编码获取默认供应商失败:', error)
  }

  return null
}



// 为所有产品设置默认供应商
const setDefaultSuppliersForProducts = async () => {
  console.log('开始为产品设置默认供应商')

  for (const item of supplierData.value) {
    if (!item.selectedSupplier) {
      // 优先根据系统编码获取默认供应商
      const defaultSupplier = await getDefaultSupplierByProductCode(item.productCode)

      if (defaultSupplier) {
        console.log(`为产品 ${item.productName} 设置默认供应商:`, defaultSupplier)
        item.selectedSupplier = defaultSupplier
      } else {
        console.log(`产品 ${item.productName} 未找到询价记录，留空供用户选择`)
        // 如果没有找到询价记录，则不设置默认供应商，留空让用户选择
        item.selectedSupplier = null
      }
    }
  }

  console.log('默认供应商设置完成')
}

// 组件挂载时获取采购员列表
onMounted(() => {
})

// 打开供应商选择弹窗
const openSupplierSelect = (record) => {
  currentProductRecord.value = record
  supplierSelectVisible.value = true
}

// 处理供应商选择确认
const handleSupplierSelect = (data) => {
  if (currentProductRecord.value && data.supplier) {
    currentProductRecord.value.selectedSupplier = {
      id: data.supplier.id,
      code: data.supplier.code,
      name: data.supplier.name
    }
  }
  supplierSelectVisible.value = false
  currentProductRecord.value = null
}

// 上传前处理
const handleBeforeUpload = async (file) => {
  console.log('准备上传文件:', file)

  // 检查文件大小（2MB限制）
  const maxSize = 2 * 1024 * 1024 // 2MB
  if (file.size > maxSize) {
    Message.error('文件大小不能超过2MB')
    return false
  }

  try {
    // 创建FormData
    const uploadFormData = new FormData()
    uploadFormData.append('file', file)
    uploadFormData.append('dir', 'purchase-applications')
    uploadFormData.append('module', 'purchase')
    uploadFormData.append('bizType', 'application')
    uploadFormData.append('bizId', '')
    uploadFormData.append('isPublic', 'true')

    console.log('FormData内容:')
    for (let [key, value] of uploadFormData.entries()) {
      console.log(key, value)
    }

    // 调用上传API
    const response = await commonApi.uploadImage(uploadFormData)

    if (response.code === 200) {
      // 上传成功，更新文件信息
      const uploadedFile = {
        uid: response.data.id,
        name: response.data.fileName || file.name,
        url: response.data.fileUrl,
        size: parseInt(response.data.fileSize) || file.size,
        type: file.type,
        status: 'done',
      }

      // 添加到文件列表
      fileList.value.push(uploadedFile)
      Message.success('文件上传成功')
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    Message.error('文件上传失败: ' + (error.message || '未知错误'))
  }

  // 返回false阻止默认上传行为
  return false
}

// 处理文件变化
const handleFileChange = (fileListData) => {
  console.log('文件变化:', fileListData)
  // 这里主要用于处理拖拽上传的文件变化
  // 实际的文件处理在handleBeforeUpload中完成
}

// 移除文件
const removeFile = (index) => {
  fileList.value.splice(index, 1)
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(1)}${units[index]}`
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 1. 先进行表单基础字段验证
    const valid = await formRef.value.validate()
    console.log('表单验证结果:', valid)
    console.log('当前表单数据:', formData)

    if (valid) {
      Message.error('请检查必填项')
      console.log('表单验证失败，错误信息:', valid)
      return
    }

    // 2. 验证必填字段
    const requiredFields = [
      { field: 'sourceSupplier', name: '采购员' },
      { field: 'actualReceiver', name: '实际收货人' },
      { field: 'contactPhone', name: '联系电话' },
      { field: 'actualAddress', name: '实际收货地址' }
    ]

    for (const { field, name } of requiredFields) {
      if (!formData[field] || formData[field].trim() === '') {
        Message.error(`${name}不能为空`)
        return
      }
    }

    // 3. 验证手机号格式
    const phonePattern = /^1[3-9]\d{9}$/
    if (!phonePattern.test(formData.contactPhone)) {
      Message.error('请输入正确的手机号码')
      return
    }

    // 4. 获取当前用户信息
    const currentUser = getCurrentUserInfo()

    // 5. 构建提交数据
    const submitData = {
      orderId: props.orderData?.id,
      purchaserId: formData.sourceSupplier, // 采购员ID
      sourceSupplier: selectedPurchaserInfo.value?.username || selectedPurchaserInfo.value?.name || '', // 采购员username
      actualReceiver: formData.actualReceiver,
      contactPhone: formData.contactPhone,
      actualAddress: formData.actualAddress,
      businessManager: formData.businessManager,
      orderRemark: formData.orderRemark,
      customerRemark: formData.customerRemark,
      suggestedSuppliers: supplierData.value.map((item, index) => {
        console.log(`构建建议供应商数据 - 索引 ${index}:`, {
          id: item.id,
          productCode: item.productCode,
          productName: item.productName,
          selectedSupplier: item.selectedSupplier
        });

        return {
          productId: item.id,
          productCode: item.productCode,
          productName: item.productName,
          supplierId: item.selectedSupplier?.id,
          supplierCode: item.selectedSupplier?.code,
          supplierName: item.selectedSupplier?.name
        };
      }),
      attachments: fileList.value.map(file => ({
        id: file.uid,
        name: file.name,
        size: file.size,
        type: file.type,
        url: file.url || ''
      })),
      submittedAt: new Date().toISOString(),
      submittedBy: currentUser?.username || currentUser?.nickname || '当前用户'
    }

    console.log('提交采购申请数据:', submitData)
    console.log('采购员信息:', {
      purchaserId: submitData.purchaserId,
      sourceSupplier: submitData.sourceSupplier,
      selectedPurchaserInfo: selectedPurchaserInfo.value
    })

    emit('submit', submitData)
    // 不在这里显示成功消息，让父组件处理
    // Message.success('申请采购提交成功')
    // visible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
    Message.error('表单验证失败，请检查输入内容')
  }
}

// 取消操作
const handleCancel = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  fileList.value = []
  selectedPurchaserInfo.value = null // 重置采购员信息
  // 重置供应商选择为默认值
  supplierData.value.forEach(item => {
    item.selectedSupplier = null
  })
  // 重新设置默认供应商
  setDefaultSuppliersForProducts()
  formRef.value?.resetFields()
}

// 初始化表单数据
const initFormData = () => {
  if (props.orderData) {
    formData.actualReceiver = props.orderData.recipientName || ''
    formData.contactPhone = props.orderData.recipientPhone || ''
    formData.actualAddress = props.orderData.address || ''
    formData.customerRemark = props.orderData.remark || ''

    // 根据订单商品数据初始化供应商表格
    if (props.orderData.products && props.orderData.products.length > 0) {
      supplierData.value = props.orderData.products.map((product, index) => {
        // 获取商品编码，优先使用goodsSkuId，然后是sku，最后是其他可能的字段
        const productCode = product.goodsSkuId || product.sku || product.goods_sku_id || product.skuCode || '';

        console.log('初始化供应商数据 - 商品信息:', {
          index,
          productName: product.productName,
          goodsSkuId: product.goodsSkuId,
          sku: product.sku,
          goods_sku_id: product.goods_sku_id,
          skuCode: product.skuCode,
          finalProductCode: productCode
        });

        return {
          id: index + 1,
          productCode: productCode,
          thrirdPartyCode: product.thrirdPartyCode || `TP${productCode}`,
          productName: product.productName || '',
          selectedSupplier: null
        };
      })

      // 为所有产品设置默认供应商
      setDefaultSuppliersForProducts()
    }
  }
}
</script>

<style scoped>
.purchase-application-modal :deep(.arco-modal-body) {
  max-height: 80vh;
  overflow-y: auto;
  padding: 20px;
}

.modal-content {
  width: 100%;
}

.purchase-form {
  width: 100%;
}

/* 主要内容区域 */
.form-main {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
}

/* 左侧表单 */
.form-left {
  flex: 1;
  min-width: 0;
}

/* 右侧表单 */
.form-right {
  flex: 1;
  min-width: 0;
}

/* 必填项标识 */
.form-item-required :deep(.arco-form-item-label)::before {
}

/* 表单项样式调整 */
.purchase-form :deep(.arco-form-item-label) {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.purchase-form :deep(.arco-form-item) {
  margin-bottom: 16px;
}

.purchase-form :deep(.arco-textarea) {
  resize: vertical;
}

.purchase-form :deep(.arco-input),
.purchase-form :deep(.arco-textarea) {
  border-radius: 4px;
}

/* 供应商表格区域 */
.supplier-section {
  width: 100%;
  margin-top: 20px;
}

/* 供应商表格样式 */
.supplier-table {
  background: white;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  width: 100%;
}

.supplier-table :deep(.arco-table-th) {
  background-color: #f7f8fa;
  font-weight: 500;
  border-bottom: 1px solid #e5e6eb;
}

.supplier-table :deep(.arco-table-td) {
  border-bottom: 1px solid #f0f0f0;
}

.supplier-table :deep(.arco-table-tbody .arco-table-tr:last-child .arco-table-td) {
  border-bottom: none;
}

/* 供应商选择单元格样式 */
.supplier-select-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
}

.selected-supplier {
  flex: 1;
  color: #333;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.supplier-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.supplier-name {
  font-weight: 500;
  color: #1d2129;
}

.supplier-code {
  color: #86909c;
  font-size: 12px;
}

.quotation-info {
  display: flex;
  align-items: center;
}

.price-tag {
  background: #e8f4ff;
  color: #165dff;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
  font-weight: 500;
}

.no-supplier {
  flex: 1;
  color: #999;
  font-size: 14px;
  font-style: italic;
}

.select-btn {
  flex-shrink: 0;
  min-width: 60px;
}

/* 上传区域样式 */
.upload-container {
  width: 100%;
}

.upload-area {
  width: 100%;
}

.upload-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 16px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  min-height: 120px;
}

.upload-button:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.upload-icon {
  font-size: 40px;
  color: #d9d9d9;
  margin-bottom: 8px;
}

.upload-text {
  text-align: center;
}

.upload-title {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.upload-link {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
  text-align: center;
  line-height: 1.4;
}

/* 文件列表样式 */
.file-list {
  margin-top: 16px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  background: white;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.file-icon {
  font-size: 16px;
  color: #1890ff;
  margin-right: 8px;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.file-actions {
  flex-shrink: 0;
  margin-left: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .purchase-application-modal {
    width: 95% !important;
  }

  .form-main {
    flex-direction: column;
    gap: 16px;
  }

  .upload-button {
    padding: 20px 16px;
    min-height: 100px;
  }

  .upload-icon {
    font-size: 32px;
  }
}

/* 模态框标题样式 */
.purchase-application-modal :deep(.arco-modal-header) {
  border-bottom: 1px solid #e5e6eb;
  padding: 16px 20px;
}

.purchase-application-modal :deep(.arco-modal-title) {
  font-size: 16px;
  font-weight: 600;
}

/* 自定义按钮区域样式 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0 0 0;
  border-top: 1px solid #e5e6eb;
  margin-top: 20px;
}

/* 模态框底部按钮样式 */
.purchase-application-modal :deep(.arco-modal-footer) {
  border-top: 1px solid #e5e6eb;
  padding: 12px 20px;
}
</style>
